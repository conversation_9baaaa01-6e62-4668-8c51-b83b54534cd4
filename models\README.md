# Models Directory

This directory contains the trained sentiment analysis models used by the application.

## Structure

```
models/
├── sentiment_model/          # Main sentiment analysis model
│   ├── config.json          # Model configuration
│   ├── pytorch_model.bin    # Model weights
│   ├── tokenizer.json       # Tokenizer configuration
│   ├── tokenizer_config.json
│   └── vocab.txt            # Vocabulary file
└── README.md               # This file
```

## Model Information

The sentiment analysis model is based on XLM-RoBERTa (Cross-lingual Language Model - Robustly Optimized BERT Pretraining Approach), fine-tuned on the Amazon Reviews Multi dataset for multilingual sentiment classification.

### Supported Languages
- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Japanese (ja)
- Chinese (zh)

### Model Classes
- **Negative**: Sentiment score 0 (1-2 stars)
- **Neutral**: Sentiment score 1 (3 stars)
- **Positive**: Sentiment score 2 (4-5 stars)

## Training

To train the model from scratch, run:

```bash
python scripts/02_train_sentiment_model.py
```

This will:
1. Download the Amazon Reviews Multi dataset
2. Preprocess the data
3. Fine-tune the XLM-RoBERTa model
4. Save the trained model to this directory

## Usage

The model is automatically loaded by the FastAPI backend when the application starts. Ensure the model files are present in the `sentiment_model/` subdirectory before running the application.

---
*Developed from Hasif's Workspace*

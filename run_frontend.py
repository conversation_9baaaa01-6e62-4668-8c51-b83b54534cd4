#!/usr/bin/env python3
"""
Frontend Runner Script for Multilingual Sentiment Analysis Platform

This script provides a convenient way to run the Streamlit frontend
with proper configuration and error handling.

Developed from Hasif's Workspace
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to run the frontend server"""
    
    # Configuration
    host = os.getenv("STREAMLIT_SERVER_ADDRESS", "0.0.0.0")
    port = os.getenv("STREAMLIT_SERVER_PORT", "8501")
    api_base_url = os.getenv("API_BASE_URL", "http://localhost:8000")
    
    logger.info(f"Starting Multilingual Sentiment Analysis Platform Frontend")
    logger.info(f"Host: {host}")
    logger.info(f"Port: {port}")
    logger.info(f"API Base URL: {api_base_url}")
    
    try:
        # Check if frontend app exists
        app_path = Path("frontend/app.py")
        if not app_path.exists():
            logger.error(f"Frontend app not found: {app_path}")
            sys.exit(1)
        
        # Prepare streamlit command
        cmd = [
            "streamlit", "run", str(app_path),
            "--server.address", host,
            "--server.port", port,
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        # Set environment variables
        env = os.environ.copy()
        env["API_BASE_URL"] = api_base_url
        
        logger.info("Starting Streamlit server...")
        
        # Start the server
        subprocess.run(cmd, env=env, check=True)
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error starting Streamlit server: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

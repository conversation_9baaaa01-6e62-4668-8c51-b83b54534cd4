# Makefile for Multilingual Sentiment Analysis Platform
# Developed from <PERSON><PERSON>'s Workspace

.PHONY: help install install-dev setup clean test test-coverage lint format docker-build docker-run docker-stop run-backend run-frontend

# Default target
help:
	@echo "Available commands:"
	@echo "  install       - Install production dependencies"
	@echo "  install-dev   - Install development dependencies"
	@echo "  setup         - Set up development environment"
	@echo "  clean         - Clean up temporary files"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  lint          - Run linting checks"
	@echo "  format        - Format code"
	@echo "  docker-build  - Build Docker images"
	@echo "  docker-run    - Run application with Docker Compose"
	@echo "  docker-stop   - Stop Docker containers"
	@echo "  run-backend   - Run backend server locally"
	@echo "  run-frontend  - Run frontend application locally"
	@echo "  download-data - Download training data"
	@echo "  train-model   - Train sentiment analysis model"

# Installation targets
install:
	pip install -r requirements.txt

install-dev: install
	pip install pytest pytest-cov black isort flake8 mypy

setup:
	python setup.py

# Cleaning
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage
	rm -rf dist
	rm -rf build

# Testing
test:
	pytest

test-coverage:
	pytest --cov=. --cov-report=html --cov-report=term

# Code quality
lint:
	flake8 backend/ frontend/ scripts/ tests/
	mypy backend/ --ignore-missing-imports

format:
	black backend/ frontend/ scripts/ tests/
	isort backend/ frontend/ scripts/ tests/

# Docker commands
docker-build:
	docker-compose build

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f

# Local development
run-backend:
	python run_backend.py

run-frontend:
	python run_frontend.py

# Data and model
download-data:
	python scripts/01_download_data.py

train-model:
	python scripts/02_train_sentiment_model.py

# Development workflow
dev-setup: setup install-dev download-data
	@echo "Development environment ready!"

dev-test: format lint test
	@echo "All checks passed!"

# Production deployment
prod-build: clean docker-build
	@echo "Production build complete!"

# Quick start
quick-start: dev-setup
	@echo "Starting services..."
	@echo "Run 'make run-backend' in one terminal"
	@echo "Run 'make run-frontend' in another terminal"
	@echo "Or use 'make docker-run' for containerized deployment"

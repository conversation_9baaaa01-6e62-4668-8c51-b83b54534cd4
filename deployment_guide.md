# Deployment Guide

This guide provides detailed instructions for deploying the Multilingual Sentiment Analysis Platform in various environments.

## Table of Contents

1. [Local Development](#local-development)
2. [Docker Deployment](#docker-deployment)
3. [Cloud Deployment](#cloud-deployment)
4. [Environment Variables](#environment-variables)
5. [Troubleshooting](#troubleshooting)

## Local Development

### Prerequisites

- Python 3.9+
- pip
- Virtual environment (recommended)

### Setup Steps

1. **Clone the repository:**
   ```bash
   git clone <repository_url>
   cd multilingual-sentiment-analysis-platform
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Download data and train model:**
   ```bash
   python scripts/01_download_data.py
   python scripts/02_train_sentiment_model.py
   ```

5. **Run backend:**
   ```bash
   uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000
   ```

6. **Run frontend (in new terminal):**
   ```bash
   streamlit run frontend/app.py
   ```

## Docker Deployment

### Using Docker Compose (Recommended)

1. **Build and start services:**
   ```bash
   docker-compose up --build
   ```

2. **Access services:**
   - Frontend: http://localhost:8501
   - Backend API: http://localhost:8000
   - API Docs: http://localhost:8000/docs

3. **Stop services:**
   ```bash
   docker-compose down
   ```

### Individual Docker Containers

1. **Build backend image:**
   ```bash
   docker build -f backend/Dockerfile -t sentiment-backend .
   ```

2. **Build frontend image:**
   ```bash
   docker build -f frontend/Dockerfile -t sentiment-frontend .
   ```

3. **Run backend container:**
   ```bash
   docker run -p 8000:8000 sentiment-backend
   ```

4. **Run frontend container:**
   ```bash
   docker run -p 8501:8501 -e API_BASE_URL=http://localhost:8000 sentiment-frontend
   ```

## Cloud Deployment

### AWS Deployment

#### Using AWS ECS with Fargate

1. **Push images to ECR:**
   ```bash
   # Create ECR repositories
   aws ecr create-repository --repository-name sentiment-backend
   aws ecr create-repository --repository-name sentiment-frontend
   
   # Build and push images
   docker build -f backend/Dockerfile -t sentiment-backend .
   docker tag sentiment-backend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/sentiment-backend:latest
   docker push <account-id>.dkr.ecr.<region>.amazonaws.com/sentiment-backend:latest
   
   docker build -f frontend/Dockerfile -t sentiment-frontend .
   docker tag sentiment-frontend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/sentiment-frontend:latest
   docker push <account-id>.dkr.ecr.<region>.amazonaws.com/sentiment-frontend:latest
   ```

2. **Create ECS task definition and service**
3. **Configure load balancer**
4. **Set up auto-scaling**

#### Using AWS Lambda (API only)

1. **Install serverless framework:**
   ```bash
   npm install -g serverless
   ```

2. **Create serverless configuration**
3. **Deploy:**
   ```bash
   serverless deploy
   ```

### Google Cloud Platform

#### Using Cloud Run

1. **Build and push to Container Registry:**
   ```bash
   gcloud builds submit --tag gcr.io/PROJECT_ID/sentiment-backend backend/
   gcloud builds submit --tag gcr.io/PROJECT_ID/sentiment-frontend frontend/
   ```

2. **Deploy to Cloud Run:**
   ```bash
   gcloud run deploy sentiment-backend --image gcr.io/PROJECT_ID/sentiment-backend --platform managed
   gcloud run deploy sentiment-frontend --image gcr.io/PROJECT_ID/sentiment-frontend --platform managed
   ```

### Azure Deployment

#### Using Azure Container Instances

1. **Create resource group:**
   ```bash
   az group create --name sentiment-analysis --location eastus
   ```

2. **Deploy containers:**
   ```bash
   az container create --resource-group sentiment-analysis --name sentiment-backend --image sentiment-backend:latest --ports 8000
   az container create --resource-group sentiment-analysis --name sentiment-frontend --image sentiment-frontend:latest --ports 8501
   ```

## Environment Variables

### Backend Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MODEL_DIR` | Path to the sentiment model | `../models/sentiment_model` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `HOST` | Server host | `0.0.0.0` |
| `PORT` | Server port | `8000` |

### Frontend Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `API_BASE_URL` | Backend API URL | `http://localhost:8000` |
| `STREAMLIT_SERVER_ADDRESS` | Streamlit server address | `0.0.0.0` |
| `STREAMLIT_SERVER_PORT` | Streamlit server port | `8501` |

## Production Considerations

### Security

1. **Use HTTPS in production**
2. **Implement API rate limiting**
3. **Add authentication if needed**
4. **Validate and sanitize inputs**
5. **Use environment variables for secrets**

### Performance

1. **Use GPU for model inference if available**
2. **Implement model caching**
3. **Use a reverse proxy (nginx)**
4. **Enable compression**
5. **Monitor resource usage**

### Monitoring

1. **Set up logging aggregation**
2. **Implement health checks**
3. **Monitor API response times**
4. **Track error rates**
5. **Set up alerts**

## Troubleshooting

### Common Issues

1. **Model not loading:**
   - Check if model files exist in `models/sentiment_model/`
   - Verify file permissions
   - Check available memory

2. **API connection errors:**
   - Verify backend is running
   - Check firewall settings
   - Confirm correct API_BASE_URL

3. **Docker build failures:**
   - Check Dockerfile syntax
   - Verify base image availability
   - Check network connectivity

4. **Memory issues:**
   - Reduce batch size for training
   - Use CPU instead of GPU if memory limited
   - Increase Docker memory limits

### Logs

- **Backend logs:** Check uvicorn output or container logs
- **Frontend logs:** Check streamlit output or container logs
- **Docker logs:** `docker-compose logs` or `docker logs <container_name>`

### Performance Optimization

1. **Model optimization:**
   - Use model quantization
   - Implement model pruning
   - Use ONNX for faster inference

2. **Caching:**
   - Implement Redis for response caching
   - Cache model predictions
   - Use CDN for static assets

---

*Developed from Hasif's Workspace*

# API Documentation

This document provides comprehensive documentation for the Multilingual Sentiment Analysis Platform API.

## Base URL

```
http://localhost:8000
```

For production deployments, replace with your actual domain.

## Authentication

Currently, the API does not require authentication. This may change in future versions.

## Endpoints

### Health Check

Check the health status of the API and model loading status.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "ok",
  "model_loaded": true
}
```

**Status Codes:**
- `200`: Service is healthy
- `503`: Service unavailable (model not loaded)

---

### Sentiment Analysis

Analyze the sentiment of a given text.

**Endpoint:** `POST /predict`

**Request Body:**
```json
{
  "text": "Your text to analyze here"
}
```

**Response:**
```json
{
  "sentiment": "positive",
  "predictions": [
    {
      "label": "positive",
      "score": 0.85
    },
    {
      "label": "neutral", 
      "score": 0.10
    },
    {
      "label": "negative",
      "score": 0.05
    }
  ]
}
```

**Parameters:**
- `text` (string, required): The text to analyze. Must be non-empty and less than 5000 characters.

**Status Codes:**
- `200`: Successful analysis
- `400`: Bad request (empty text, invalid format)
- `422`: Validation error
- `503`: Service unavailable (model not loaded)
- `500`: Internal server error

---

## Error Responses

All error responses follow this format:

```json
{
  "detail": "Error message describing what went wrong"
}
```

### Common Error Messages

- `"Input text cannot be empty."` - The provided text is empty or whitespace only
- `"Model not loaded. Cannot perform predictions."` - The sentiment model failed to load
- `"An unexpected error occurred: [details]"` - Internal server error

## Rate Limiting

Currently, there are no rate limits imposed. This may change in production deployments.

## Supported Languages

The API supports sentiment analysis for the following languages:

- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Japanese (ja)
- Chinese (zh)

## Sentiment Labels

The API returns one of three sentiment labels:

- **positive**: Indicates positive sentiment (equivalent to 4-5 star ratings)
- **neutral**: Indicates neutral sentiment (equivalent to 3 star ratings)
- **negative**: Indicates negative sentiment (equivalent to 1-2 star ratings)

## Usage Examples

### Python

```python
import requests

# Basic usage
response = requests.post(
    "http://localhost:8000/predict",
    json={"text": "I love this product!"}
)

if response.status_code == 200:
    result = response.json()
    print(f"Sentiment: {result['sentiment']}")
    print(f"Confidence: {result['predictions'][0]['score']:.2f}")
else:
    print(f"Error: {response.status_code}")
```

### JavaScript

```javascript
// Using fetch API
async function analyzeSentiment(text) {
    try {
        const response = await fetch('http://localhost:8000/predict', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: text })
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('Sentiment:', result.sentiment);
            console.log('Confidence:', result.predictions[0].score);
        } else {
            console.error('Error:', response.status);
        }
    } catch (error) {
        console.error('Network error:', error);
    }
}

// Usage
analyzeSentiment("This is an amazing product!");
```

### cURL

```bash
# Basic sentiment analysis
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "I love this product!"}'

# Health check
curl -X GET "http://localhost:8000/health"
```

## Interactive Documentation

The API provides interactive documentation using Swagger UI:

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

These interfaces allow you to:
- Explore all available endpoints
- Test API calls directly from the browser
- View detailed request/response schemas
- Download OpenAPI specification

## Performance Considerations

- **Response Time**: Typical response time is under 2 seconds for standard text inputs
- **Text Length**: Longer texts (>1000 characters) may take slightly longer to process
- **Concurrent Requests**: The API can handle multiple concurrent requests
- **Memory Usage**: Each request uses approximately 100-200MB of memory during processing

## Best Practices

1. **Input Validation**: Always validate text input on the client side before sending to the API
2. **Error Handling**: Implement proper error handling for all possible status codes
3. **Timeout**: Set appropriate timeouts for API calls (recommended: 30 seconds)
4. **Retry Logic**: Implement retry logic for transient failures (5xx errors)
5. **Text Preprocessing**: Remove excessive whitespace and special characters before analysis

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure the API server is running on the correct port
2. **Model Not Loaded**: Check server logs for model loading errors
3. **Timeout Errors**: Increase timeout values for longer texts
4. **Memory Errors**: Reduce concurrent requests or text length

### Debug Information

Enable debug mode by setting `DEBUG=true` in environment variables for detailed error messages and logging.

---

*Developed from Hasif's Workspace*

#!/usr/bin/env python3
"""
Backend Runner Script for Multilingual Sentiment Analysis Platform

This script provides a convenient way to run the FastAPI backend server
with proper configuration and error handling.

Developed from Hasif's Workspace
"""

import os
import sys
import uvicorn
import logging
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to run the backend server"""
    
    # Configuration
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    reload = os.getenv("RELOAD", "true").lower() == "true"
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    
    logger.info(f"Starting Multilingual Sentiment Analysis Platform Backend")
    logger.info(f"Host: {host}")
    logger.info(f"Port: {port}")
    logger.info(f"Reload: {reload}")
    logger.info(f"Log Level: {log_level}")
    
    try:
        # Check if model directory exists
        model_dir = Path("models/sentiment_model")
        if not model_dir.exists():
            logger.warning(f"Model directory not found: {model_dir}")
            logger.warning("The API will start but predictions will fail until a model is available")
            logger.info("Run 'python scripts/02_train_sentiment_model.py' to train a model")
        
        # Start the server
        uvicorn.run(
            "backend.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level=log_level,
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

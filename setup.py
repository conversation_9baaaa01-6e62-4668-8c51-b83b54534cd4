#!/usr/bin/env python3
"""
Setup Script for Multilingual Sentiment Analysis Platform

This script helps set up the development environment and install dependencies.

Developed from Hasif's Workspace
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(cmd, description):
    """Run a command and handle errors"""
    logger.info(f"{description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed: {e}")
        if e.stdout:
            logger.error(f"STDOUT: {e.stdout}")
        if e.stderr:
            logger.error(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        logger.error("❌ Python 3.9 or higher is required")
        return False
    logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def setup_virtual_environment():
    """Set up virtual environment"""
    if os.path.exists("venv"):
        logger.info("Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")

def install_dependencies():
    """Install project dependencies"""
    # Determine the activation script based on OS
    if os.name == 'nt':  # Windows
        activate_script = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        activate_script = "venv/bin/activate"
        pip_cmd = "venv/bin/pip"
    
    # Install main dependencies
    if not run_command(f"{pip_cmd} install -r requirements.txt", "Installing main dependencies"):
        return False
    
    # Install backend dependencies
    if not run_command(f"{pip_cmd} install -r backend/requirements.txt", "Installing backend dependencies"):
        return False
    
    # Install frontend dependencies
    if not run_command(f"{pip_cmd} install -r frontend/requirements.txt", "Installing frontend dependencies"):
        return False
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "models/sentiment_model",
        "data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ Created directory: {directory}")
    
    return True

def download_nltk_data():
    """Download required NLTK data"""
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        python_cmd = "venv/bin/python"
    
    nltk_downloads = [
        "punkt",
        "stopwords", 
        "wordnet",
        "averaged_perceptron_tagger"
    ]
    
    for data in nltk_downloads:
        cmd = f'{python_cmd} -c "import nltk; nltk.download(\'{data}\')"'
        if not run_command(cmd, f"Downloading NLTK data: {data}"):
            logger.warning(f"⚠️ Failed to download NLTK data: {data}")
    
    return True

def download_spacy_model():
    """Download spaCy model"""
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        python_cmd = "venv/bin/python"
    
    return run_command(f"{python_cmd} -m spacy download en_core_web_sm", "Downloading spaCy English model")

def main():
    """Main setup function"""
    logger.info("🚀 Setting up Multilingual Sentiment Analysis Platform")
    logger.info("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Set up virtual environment
    if not setup_virtual_environment():
        sys.exit(1)
    
    # Create necessary directories
    if not create_directories():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Download NLTK data
    download_nltk_data()
    
    # Download spaCy model
    download_spacy_model()
    
    logger.info("=" * 60)
    logger.info("🎉 Setup completed successfully!")
    logger.info("")
    logger.info("Next steps:")
    logger.info("1. Activate virtual environment:")
    if os.name == 'nt':  # Windows
        logger.info("   venv\\Scripts\\activate")
    else:  # Unix/Linux/macOS
        logger.info("   source venv/bin/activate")
    logger.info("")
    logger.info("2. Download data and train model (optional):")
    logger.info("   python scripts/01_download_data.py")
    logger.info("   python scripts/02_train_sentiment_model.py")
    logger.info("")
    logger.info("3. Run the application:")
    logger.info("   Option A - Using Docker: docker-compose up --build")
    logger.info("   Option B - Local development:")
    logger.info("     Terminal 1: python run_backend.py")
    logger.info("     Terminal 2: python run_frontend.py")
    logger.info("")
    logger.info("4. Access the application:")
    logger.info("   Frontend: http://localhost:8501")
    logger.info("   Backend API: http://localhost:8000")
    logger.info("   API Docs: http://localhost:8000/docs")
    logger.info("")
    logger.info("Developed from Hasif's Workspace")

if __name__ == "__main__":
    main()

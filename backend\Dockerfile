# Start from a Python base image
FROM python:3.9-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file from the backend directory first to leverage Docker layer caching
COPY requirements.txt .

# Install FastAPI, Uvicorn, and other dependencies from requirements.txt
# Ensure torch is listed in requirements.txt or install it specifically if needed
# Adding fastapi and uvicorn here directly as they are core to this API
# and ensures they are present even if requirements.txt is minimal.
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir fastapi uvicorn[standard] python-multipart

# Copy the rest of the backend directory into the /app directory in the container
COPY . /app

# Copy the models directory.
# This assumes your trained model is in ../models/sentiment_model relative to Dockerfile context.
# For large models, consider using multi-stage builds or mounting volumes in production.
COPY ../models/ /app/models/

# Expose the port the app runs on
EXPOSE 8000

# Command to run the FastAPI application using Uvicorn
# It looks for an 'app' instance in a file named 'main.py' within the /app directory.
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

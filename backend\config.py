"""
Configuration module for Multilingual Sentiment Analysis Platform Backend

This module contains configuration settings and environment variable handling
for the FastAPI backend application.

Developed from Hasif's Workspace
"""

import os
from pathlib import Path
from typing import Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application settings
    app_name: str = Field(default="Multilingual Sentiment Analysis Platform", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    reload: bool = Field(default=False, env="RELOAD")
    
    # Model settings
    model_dir: str = Field(default="../models/sentiment_model", env="MODEL_DIR")
    model_name: str = Field(default="xlm-roberta-base", env="MODEL_NAME")
    max_sequence_length: int = Field(default=512, env="MAX_SEQUENCE_LENGTH")
    
    # Device settings
    use_gpu: bool = Field(default=True, env="USE_GPU")
    device_id: int = Field(default=0, env="DEVICE_ID")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    # API settings
    api_prefix: str = Field(default="", env="API_PREFIX")
    cors_origins: list = Field(default=["*"], env="CORS_ORIGINS")
    max_request_size: int = Field(default=1024 * 1024, env="MAX_REQUEST_SIZE")  # 1MB
    
    # Performance settings
    workers: int = Field(default=1, env="WORKERS")
    timeout: int = Field(default=30, env="TIMEOUT")
    
    # Security settings
    allowed_hosts: list = Field(default=["*"], env="ALLOWED_HOSTS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class ModelConfig:
    """Model-specific configuration"""
    
    # Sentiment labels mapping
    ID2LABEL = {0: "negative", 1: "neutral", 2: "positive"}
    LABEL2ID = {"negative": 0, "neutral": 1, "positive": 2}
    
    # Model parameters
    NUM_LABELS = 3
    
    # Supported languages
    SUPPORTED_LANGUAGES = ["en", "es", "fr", "de", "ja", "zh"]
    
    # Training configuration (for reference)
    TRAINING_CONFIG = {
        "learning_rate": 2e-5,
        "batch_size": 16,
        "num_epochs": 3,
        "warmup_steps": 500,
        "weight_decay": 0.01,
        "max_grad_norm": 1.0
    }


class APIConfig:
    """API-specific configuration"""
    
    # Response formats
    RESPONSE_MODELS = {
        "sentiment_response": {
            "predictions": "list[dict]",
            "sentiment": "str"
        }
    }
    
    # Error messages
    ERROR_MESSAGES = {
        "model_not_loaded": "Model not loaded. Cannot perform predictions.",
        "empty_text": "Input text cannot be empty.",
        "invalid_input": "Invalid input format.",
        "processing_error": "Error processing text with sentiment pipeline.",
        "server_error": "An unexpected error occurred."
    }
    
    # Rate limiting (if implemented)
    RATE_LIMIT = {
        "requests_per_minute": 60,
        "requests_per_hour": 1000
    }


def get_settings() -> Settings:
    """Get application settings instance"""
    return Settings()


def get_model_path() -> Path:
    """Get the model directory path"""
    settings = get_settings()
    model_path = Path(settings.model_dir)
    
    # Make path absolute if it's relative
    if not model_path.is_absolute():
        # Assume relative to the backend directory
        backend_dir = Path(__file__).parent
        model_path = backend_dir / model_path
    
    return model_path.resolve()


def validate_model_path() -> bool:
    """Validate that the model path exists and contains required files"""
    model_path = get_model_path()
    
    if not model_path.exists():
        return False
    
    # Check for required model files
    required_files = [
        "config.json",
        "pytorch_model.bin",
        "tokenizer.json",
        "tokenizer_config.json"
    ]
    
    for file_name in required_files:
        if not (model_path / file_name).exists():
            return False
    
    return True


def get_device_config() -> tuple[str, int]:
    """Get device configuration for model loading"""
    settings = get_settings()
    
    if settings.use_gpu:
        try:
            import torch
            if torch.cuda.is_available():
                device = f"cuda:{settings.device_id}"
                device_id = settings.device_id
            else:
                device = "cpu"
                device_id = -1
        except ImportError:
            device = "cpu"
            device_id = -1
    else:
        device = "cpu"
        device_id = -1
    
    return device, device_id


# Global settings instance
settings = get_settings()
model_config = ModelConfig()
api_config = APIConfig()

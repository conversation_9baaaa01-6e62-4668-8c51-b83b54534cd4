#!/usr/bin/env python3
"""
Environment Setup Script for Multilingual Sentiment Analysis Platform

This script helps set up the complete development environment including
downloading required models and data.

Developed from Hasif's Workspace
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
import shutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        logger.error("❌ Python 3.9 or higher is required")
        return False
    logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_git():
    """Check if git is available"""
    try:
        subprocess.run(["git", "--version"], capture_output=True, check=True)
        logger.info("✅ Git is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning("⚠️ Git not found - some features may not work")
        return False

def check_docker():
    """Check if Docker is available"""
    try:
        subprocess.run(["docker", "--version"], capture_output=True, check=True)
        logger.info("✅ Docker is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning("⚠️ Docker not found - containerized deployment won't work")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "models",
        "models/sentiment_model",
        "data",
        "notebooks",
        ".vscode",
        "docs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ Created/verified directory: {directory}")

def create_vscode_settings():
    """Create VS Code settings for the project"""
    vscode_settings = {
        "python.defaultInterpreterPath": "./venv/bin/python",
        "python.linting.enabled": True,
        "python.linting.pylintEnabled": False,
        "python.linting.flake8Enabled": True,
        "python.formatting.provider": "black",
        "python.sortImports.args": ["--profile", "black"],
        "editor.formatOnSave": True,
        "editor.codeActionsOnSave": {
            "source.organizeImports": True
        },
        "files.exclude": {
            "**/__pycache__": True,
            "**/*.pyc": True,
            ".pytest_cache": True,
            "htmlcov": True,
            ".coverage": True
        }
    }
    
    import json
    vscode_dir = Path(".vscode")
    vscode_dir.mkdir(exist_ok=True)
    
    with open(vscode_dir / "settings.json", "w") as f:
        json.dump(vscode_settings, f, indent=2)
    
    logger.info("✅ Created VS Code settings")

def create_gitignore_additions():
    """Add project-specific entries to .gitignore if needed"""
    additional_entries = [
        "# Project specific",
        ".env",
        "logs/*.log",
        "models/sentiment_model/*",
        "!models/sentiment_model/.gitkeep",
        "data/*.csv",
        "data/*.json",
        "data/*.parquet",
        "notebooks/*.ipynb",
        "!notebooks/example.ipynb"
    ]
    
    gitignore_path = Path(".gitignore")
    if gitignore_path.exists():
        with open(gitignore_path, "r") as f:
            content = f.read()
        
        # Check if our entries are already there
        if "# Project specific" not in content:
            with open(gitignore_path, "a") as f:
                f.write("\n" + "\n".join(additional_entries) + "\n")
            logger.info("✅ Updated .gitignore with project-specific entries")
    else:
        logger.warning("⚠️ .gitignore not found")

def create_example_notebook():
    """Create an example Jupyter notebook"""
    notebook_content = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# Multilingual Sentiment Analysis Platform - Example Notebook\n",
                    "\n",
                    "This notebook demonstrates how to use the sentiment analysis platform.\n",
                    "\n",
                    "*Developed from Hasif's Workspace*"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "import requests\n",
                    "import pandas as pd\n",
                    "import plotly.express as px\n",
                    "\n",
                    "# API endpoint\n",
                    "API_URL = \"http://localhost:8000/predict\"\n",
                    "\n",
                    "# Example texts in different languages\n",
                    "texts = [\n",
                    "    \"I love this product!\",\n",
                    "    \"Este producto es terrible\",\n",
                    "    \"Ce produit est correct\",\n",
                    "    \"Dieses Produkt ist großartig\"\n",
                    "]\n",
                    "\n",
                    "print(\"Example texts for sentiment analysis:\")\n",
                    "for i, text in enumerate(texts, 1):\n",
                    "    print(f\"{i}. {text}\")"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Function to analyze sentiment\n",
                    "def analyze_sentiment(text):\n",
                    "    try:\n",
                    "        response = requests.post(API_URL, json={\"text\": text})\n",
                    "        if response.status_code == 200:\n",
                    "            return response.json()\n",
                    "        else:\n",
                    "            return {\"error\": f\"API error: {response.status_code}\"}\n",
                    "    except Exception as e:\n",
                    "        return {\"error\": str(e)}\n",
                    "\n",
                    "# Analyze all texts\n",
                    "results = []\n",
                    "for text in texts:\n",
                    "    result = analyze_sentiment(text)\n",
                    "    results.append({\"text\": text, \"result\": result})\n",
                    "    print(f\"Text: {text}\")\n",
                    "    print(f\"Result: {result}\")\n",
                    "    print(\"-\" * 50)"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.9.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    import json
    notebook_path = Path("notebooks/example.ipynb")
    with open(notebook_path, "w") as f:
        json.dump(notebook_content, f, indent=2)
    
    logger.info("✅ Created example Jupyter notebook")

def create_env_file():
    """Create .env file from .env.example if it doesn't exist"""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        shutil.copy(env_example, env_file)
        logger.info("✅ Created .env file from .env.example")
    elif env_file.exists():
        logger.info("✅ .env file already exists")
    else:
        logger.warning("⚠️ .env.example not found")

def main():
    """Main setup function"""
    logger.info("🚀 Setting up Multilingual Sentiment Analysis Platform Environment")
    logger.info("=" * 70)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    check_git()
    check_docker()
    
    # Create project structure
    create_directories()
    create_vscode_settings()
    create_gitignore_additions()
    create_example_notebook()
    create_env_file()
    
    logger.info("=" * 70)
    logger.info("🎉 Environment setup completed successfully!")
    logger.info("")
    logger.info("Next steps:")
    logger.info("1. Run 'python setup.py' to install dependencies")
    logger.info("2. Run 'make download-data' to download training data")
    logger.info("3. Run 'make train-model' to train the sentiment model")
    logger.info("4. Run 'make docker-run' or use individual run scripts")
    logger.info("")
    logger.info("For development:")
    logger.info("- Use VS Code for the best development experience")
    logger.info("- Check the example notebook in notebooks/example.ipynb")
    logger.info("- Use 'make help' to see all available commands")
    logger.info("")
    logger.info("Developed from Hasif's Workspace")

if __name__ == "__main__":
    main()

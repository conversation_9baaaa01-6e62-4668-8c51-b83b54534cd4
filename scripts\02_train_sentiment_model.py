"""
Model Training Script for Multilingual Sentiment Analysis Platform

This script fine-tunes a XLM-RoBERTa model on the Amazon Reviews Multi dataset
for multilingual sentiment classification.

Developed from <PERSON><PERSON>'s Workspace
"""

import pandas as pd
from datasets import load_dataset, concatenate_datasets, DatasetDict
from transformers import AutoTokenizer, AutoModelForSequenceClassification, TrainingArguments, Trainer
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
import numpy as np
import os

# Configuration
MODEL_NAME = "xlm-roberta-base"  # or "bert-base-multilingual-cased"
DATASET_NAME = "amazon_reviews_multi"
LANGUAGES_TO_TRAIN = ['en', 'es']  # Subset for initial development: ['en', 'es', 'fr', 'de', 'ja', 'zh']
NUM_LABELS = 3  # positive, negative, neutral
OUTPUT_DIR = "./models/sentiment_model"
LOGGING_DIR = "./logs/sentiment_model_logs"

# Create dirs if they don't exist
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(LOGGING_DIR, exist_ok=True)

# --- Load Dataset ---
print("Loading datasets...")
all_datasets = []

for lang in LANGUAGES_TO_TRAIN:
    print(f"Loading {DATASET_NAME} for language: {lang}")
    # Load only the train split, then we'll split it ourselves
    # Using a small max_samples for initial development to speed things up.
    # Remove/adjust max_samples for a full run.
    dataset_lang = load_dataset(DATASET_NAME, lang, split='train[:1%]', trust_remote_code=True)  # Taking 1% for faster processing
    all_datasets.append(dataset_lang)

# Concatenate datasets
# Note: For a real run, consider memory constraints and potentially stream or process data in chunks if the combined dataset is too large.
print("Concatenating datasets...")
if not all_datasets:
    raise ValueError("No datasets were loaded. Check LANGUAGES_TO_TRAIN and dataset availability.")

dataset = concatenate_datasets(all_datasets)
print(f"Combined dataset size: {len(dataset)}")

# --- Initialize Tokenizer ---
print(f"Initializing tokenizer: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)

# --- Preprocessing Function ---
def preprocess_function(examples):
    # Concatenate review title and body
    # Handle cases where 'review_title' or 'review_body' might be None or not present
    texts = []
    for i in range(len(examples['review_id'])):
        title = examples['review_title'][i] if examples['review_title'][i] else ""
        body = examples['review_body'][i] if examples['review_body'][i] else ""
        texts.append(title + " " + body)
    
    # Tokenize the text
    tokenized_inputs = tokenizer(texts, truncation=True, padding="max_length", max_length=512)
    
    # Star Rating to Sentiment: 1-2 stars -> 0 (negative), 3 stars -> 1 (neutral), 4-5 stars -> 2 (positive)
    labels = []
    for stars in examples['stars']:
        if stars in [1, 2]:
            labels.append(0)  # Negative
        elif stars == 3:
            labels.append(1)  # Neutral
        elif stars in [4, 5]:
            labels.append(2)  # Positive
        else:
            labels.append(-1)  # Should not happen with 1-5 stars
    
    tokenized_inputs['label'] = labels
    return tokenized_inputs

# --- Apply Preprocessing ---
print("Applying preprocessing...")
# Select a subset for faster preprocessing during development. Remove for full run.
# dataset = dataset.select(range(min(1000, len(dataset))))  # Limit to 1000 examples for dev

dataset = dataset.map(preprocess_function, batched=True, remove_columns=['review_id', 'product_id', 'reviewer_id', 'stars', 'review_body', 'review_title', 'language', 'product_category'])
print("Preprocessing complete.")

# Set format for PyTorch
dataset.set_format("torch")

# --- Split Dataset ---
print("Splitting dataset...")
# First, split into train and temp (validation + test)
train_val_test_split = dataset.train_test_split(test_size=0.3, shuffle=True, seed=42)  # 30% for val+test
train_dataset = train_val_test_split['train']
val_test_dataset = train_val_test_split['test']

# Split temp into validation and test
val_test_split = val_test_dataset.train_test_split(test_size=0.5, shuffle=True, seed=42)  # 50% of temp for test
val_dataset = val_test_split['train']
test_dataset = val_test_split['test']

print(f"Train dataset size: {len(train_dataset)}")
print(f"Validation dataset size: {len(val_dataset)}")
print(f"Test dataset size: {len(test_dataset)}")

# --- Initialize Model ---
print(f"Initializing model: {MODEL_NAME}")
model = AutoModelForSequenceClassification.from_pretrained(MODEL_NAME, num_labels=NUM_LABELS)

# --- Metrics Function ---
def compute_metrics(eval_pred):
    predictions, labels = eval_pred
    preds = np.argmax(predictions, axis=1)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, preds, average='weighted', zero_division=0)
    acc = accuracy_score(labels, preds)
    return {'accuracy': acc, 'f1': f1, 'precision': precision, 'recall': recall}

# --- Training Arguments ---
print("Defining training arguments...")
training_args = TrainingArguments(
    output_dir=OUTPUT_DIR,
    num_train_epochs=1,  # Keep low for initial setup (e.g., 1-3 epochs)
    per_device_train_batch_size=4,  # Adjust based on GPU memory (8 might be too high for XLM-R base on some GPUs with 512 seq len)
    per_device_eval_batch_size=4,
    warmup_steps=100,  # Reduced warmup steps due to smaller dataset/epochs
    weight_decay=0.01,
    logging_dir=LOGGING_DIR,
    logging_steps=5,  # Log more frequently for small datasets
    evaluation_strategy="epoch",
    save_strategy="epoch",
    load_best_model_at_end=True,
    report_to="tensorboard",  # or "none"
    # push_to_hub=False,  # Uncomment if you want to push to Hugging Face Hub
)

# --- Initialize Trainer ---
print("Initializing Trainer...")
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
    compute_metrics=compute_metrics,
    tokenizer=tokenizer,  # Good for ensuring tokenizer is saved with model
)

# --- Train Model ---
print("Starting training...")
try:
    trainer.train()
    print("Training finished.")
except Exception as e:
    print(f"An error occurred during training: {e}")
    print("Ensure you have 'accelerate' installed and a GPU available if not running on CPU.")
    print("Attempting to proceed with evaluation and saving if possible, but model may not be trained.")

# --- Evaluate Model ---
print("Evaluating model on the test set...")
if test_dataset and len(test_dataset) > 0:
    eval_results = trainer.evaluate(test_dataset)
    print("\nEvaluation results on Test Set:")
    for key, value in eval_results.items():
        print(f"  {key}: {value}")
else:
    print("Test dataset is empty or not available. Skipping evaluation on test set.")
    print("Evaluating on validation set instead for now.")
    eval_results_val = trainer.evaluate(val_dataset)
    print("\nEvaluation results on Validation Set:")
    for key, value in eval_results_val.items():
        print(f"  {key}: {value}")

# --- Save Model ---
print(f"Saving model to {OUTPUT_DIR}...")
trainer.save_model(OUTPUT_DIR)
tokenizer.save_pretrained(OUTPUT_DIR)
print("Model and tokenizer saved.")

print("\n--- Script Finished ---")
print(f"Output model saved in: {OUTPUT_DIR}")
print(f"Logs saved in: {LOGGING_DIR}")
print("Developed from Hasif's Workspace")

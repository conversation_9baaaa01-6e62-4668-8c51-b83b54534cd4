# Contributing to Multilingual Sentiment Analysis Platform

Thank you for your interest in contributing to the Multilingual Sentiment Analysis Platform! This document provides guidelines and information for contributors.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Setup](#development-setup)
4. [Making Changes](#making-changes)
5. [Testing](#testing)
6. [Submitting Changes](#submitting-changes)
7. [Style Guidelines](#style-guidelines)

## Code of Conduct

This project follows a simple code of conduct:
- Be respectful and inclusive
- Focus on constructive feedback
- Help maintain a welcoming environment for all contributors

## Getting Started

### Prerequisites

- Python 3.9 or higher
- Git
- Docker (optional but recommended)
- Basic knowledge of FastAPI and Streamlit

### Development Setup

1. **Fork and clone the repository:**
   ```bash
   git clone https://github.com/Hasif50/multilingual-sentiment-analysis-platform.git
   cd multilingual-sentiment-analysis-platform
   ```

2. **Set up development environment:**
   ```bash
   python setup.py
   ```

3. **Activate virtual environment:**
   ```bash
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

4. **Install development dependencies:**
   ```bash
   pip install -r requirements.txt
   pip install -e .
   ```

## Making Changes

### Branch Naming Convention

- `feature/description` - for new features
- `bugfix/description` - for bug fixes
- `docs/description` - for documentation updates
- `refactor/description` - for code refactoring

### Commit Message Format

```
type(scope): brief description

Detailed description if needed

- List any breaking changes
- Reference issues: Fixes #123
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

## Testing

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=.

# Run specific test file
pytest tests/test_backend.py

# Run tests with verbose output
pytest -v
```

### Writing Tests

- Write tests for all new functionality
- Maintain or improve test coverage
- Use descriptive test names
- Follow the existing test structure

### Test Structure

```python
class TestFeatureName:
    """Test cases for specific feature"""
    
    def test_specific_functionality(self):
        """Test description"""
        # Arrange
        # Act
        # Assert
```

## Submitting Changes

### Pull Request Process

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes and commit:**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

3. **Push to your fork:**
   ```bash
   git push origin feature/your-feature-name
   ```

4. **Create a Pull Request:**
   - Use a clear title and description
   - Reference any related issues
   - Include screenshots for UI changes
   - Ensure all tests pass

### Pull Request Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes (or clearly documented)
- [ ] Commit messages follow convention

## Style Guidelines

### Python Code Style

- Follow PEP 8
- Use type hints where appropriate
- Write docstrings for functions and classes
- Keep functions focused and small
- Use meaningful variable names

### Code Formatting

We use the following tools:
- `black` for code formatting
- `isort` for import sorting
- `flake8` for linting

```bash
# Format code
black .

# Sort imports
isort .

# Check linting
flake8 .
```

### Documentation Style

- Use clear, concise language
- Include code examples where helpful
- Update README.md for significant changes
- Document API endpoints thoroughly

## Project Structure

```
├── backend/                # FastAPI backend
├── frontend/               # Streamlit frontend
├── data/                   # Data files and documentation
├── models/                 # Model files and documentation
├── scripts/                # Utility scripts
├── tests/                  # Test suite
├── docs/                   # Additional documentation
└── requirements.txt        # Dependencies
```

## Areas for Contribution

### High Priority
- Model performance improvements
- Additional language support
- API optimization
- UI/UX enhancements

### Medium Priority
- Documentation improvements
- Test coverage expansion
- Code refactoring
- Performance monitoring

### Low Priority
- Additional visualizations
- Export functionality
- Batch processing features

## Getting Help

If you need help or have questions:

1. Check existing issues and documentation
2. Create a new issue with detailed description
3. Use appropriate labels for categorization

## Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes for significant contributions
- Project documentation

---

*Developed from Hasif's Workspace*

Thank you for contributing to the Multilingual Sentiment Analysis Platform!

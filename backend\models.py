"""
Pydantic models for Multilingual Sentiment Analysis Platform Backend

This module contains the data models used for request/response validation
and API documentation.

Developed from Hasif's Workspace
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class SentimentLabel(str, Enum):
    """Enumeration of possible sentiment labels"""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"


class SentimentRequest(BaseModel):
    """Request model for sentiment analysis"""
    
    text: str = Field(
        ...,
        description="Text to analyze for sentiment",
        example="This product is amazing! I love it.",
        min_length=1,
        max_length=5000
    )
    
    language: Optional[str] = Field(
        None,
        description="Language code (optional, auto-detected if not provided)",
        example="en",
        regex="^[a-z]{2}$"
    )
    
    return_all_scores: bool = Field(
        True,
        description="Whether to return confidence scores for all sentiment categories"
    )
    
    @validator('text')
    def text_must_not_be_empty(cls, v):
        """Validate that text is not empty or whitespace only"""
        if not v or not v.strip():
            raise ValueError('Text cannot be empty or whitespace only')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "text": "This product is amazing! I love it.",
                "language": "en",
                "return_all_scores": True
            }
        }


class SentimentPrediction(BaseModel):
    """Individual sentiment prediction with confidence score"""
    
    label: SentimentLabel = Field(
        ...,
        description="Predicted sentiment label"
    )
    
    score: float = Field(
        ...,
        description="Confidence score for this prediction",
        ge=0.0,
        le=1.0
    )
    
    class Config:
        schema_extra = {
            "example": {
                "label": "positive",
                "score": 0.85
            }
        }


class SentimentResponse(BaseModel):
    """Response model for sentiment analysis"""
    
    sentiment: SentimentLabel = Field(
        ...,
        description="Overall predicted sentiment (highest confidence)"
    )
    
    confidence: float = Field(
        ...,
        description="Confidence score for the predicted sentiment",
        ge=0.0,
        le=1.0
    )
    
    predictions: List[SentimentPrediction] = Field(
        ...,
        description="Detailed predictions for all sentiment categories"
    )
    
    processing_time: Optional[float] = Field(
        None,
        description="Processing time in seconds",
        ge=0.0
    )
    
    detected_language: Optional[str] = Field(
        None,
        description="Detected language code (if language detection is enabled)"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "sentiment": "positive",
                "confidence": 0.85,
                "predictions": [
                    {"label": "positive", "score": 0.85},
                    {"label": "neutral", "score": 0.10},
                    {"label": "negative", "score": 0.05}
                ],
                "processing_time": 0.123,
                "detected_language": "en"
            }
        }


class BatchSentimentRequest(BaseModel):
    """Request model for batch sentiment analysis"""
    
    texts: List[str] = Field(
        ...,
        description="List of texts to analyze",
        min_items=1,
        max_items=100
    )
    
    language: Optional[str] = Field(
        None,
        description="Language code for all texts (optional)",
        regex="^[a-z]{2}$"
    )
    
    return_all_scores: bool = Field(
        True,
        description="Whether to return confidence scores for all sentiment categories"
    )
    
    @validator('texts')
    def validate_texts(cls, v):
        """Validate that all texts are non-empty"""
        for i, text in enumerate(v):
            if not text or not text.strip():
                raise ValueError(f'Text at index {i} cannot be empty or whitespace only')
        return [text.strip() for text in v]
    
    class Config:
        schema_extra = {
            "example": {
                "texts": [
                    "This product is amazing!",
                    "I hate this service.",
                    "It's okay, nothing special."
                ],
                "language": "en",
                "return_all_scores": True
            }
        }


class BatchSentimentResponse(BaseModel):
    """Response model for batch sentiment analysis"""
    
    results: List[SentimentResponse] = Field(
        ...,
        description="Sentiment analysis results for each input text"
    )
    
    total_processing_time: Optional[float] = Field(
        None,
        description="Total processing time for all texts in seconds",
        ge=0.0
    )
    
    class Config:
        schema_extra = {
            "example": {
                "results": [
                    {
                        "sentiment": "positive",
                        "confidence": 0.85,
                        "predictions": [
                            {"label": "positive", "score": 0.85},
                            {"label": "neutral", "score": 0.10},
                            {"label": "negative", "score": 0.05}
                        ],
                        "processing_time": 0.123
                    }
                ],
                "total_processing_time": 0.456
            }
        }


class HealthResponse(BaseModel):
    """Response model for health check endpoint"""
    
    status: str = Field(
        ...,
        description="Service status",
        example="healthy"
    )
    
    model_loaded: bool = Field(
        ...,
        description="Whether the sentiment analysis model is loaded"
    )
    
    model_info: Optional[Dict[str, Any]] = Field(
        None,
        description="Information about the loaded model"
    )
    
    uptime: Optional[float] = Field(
        None,
        description="Service uptime in seconds",
        ge=0.0
    )
    
    version: Optional[str] = Field(
        None,
        description="API version"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "model_loaded": True,
                "model_info": {
                    "model_name": "xlm-roberta-base",
                    "num_labels": 3,
                    "supported_languages": ["en", "es", "fr", "de", "ja", "zh"]
                },
                "uptime": 3600.0,
                "version": "1.0.0"
            }
        }


class ErrorResponse(BaseModel):
    """Response model for error cases"""
    
    error: str = Field(
        ...,
        description="Error type or code"
    )
    
    message: str = Field(
        ...,
        description="Human-readable error message"
    )
    
    details: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional error details"
    )
    
    timestamp: Optional[str] = Field(
        None,
        description="Error timestamp in ISO format"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "error": "validation_error",
                "message": "Input text cannot be empty",
                "details": {
                    "field": "text",
                    "input": ""
                },
                "timestamp": "2024-01-01T12:00:00Z"
            }
        }

# Core dependencies for Multilingual Sentiment Analysis Platform
# Developed from Hasif's Workspace

# Data processing and ML
pandas==2.1.3
numpy==1.24.3
scikit-learn==1.3.2

# Hugging Face ecosystem
transformers==4.35.2
datasets==2.14.6
accelerate==0.24.1
torch==2.1.1

# Web framework and API
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Frontend
streamlit==1.28.1
plotly==5.17.0

# HTTP requests
requests==2.31.0

# Development and testing
pytest==7.4.3
pytest-cov==4.1.0
requests-mock==1.11.0

# Jupyter for notebooks
jupyterlab==4.0.8
ipykernel==6.26.0

# Utilities
python-dotenv==1.0.0

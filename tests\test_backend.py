"""
Backend API Tests for Multilingual Sentiment Analysis Platform

Tests for the FastAPI backend endpoints and functionality.

Developed from Hasif's Workspace
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from main import app

client = TestClient(app)

class TestHealthEndpoint:
    """Test cases for the health check endpoint"""
    
    def test_health_check_success(self):
        """Test health check when model is loaded successfully"""
        with patch('main.model_load_error', False):
            response = client.get("/health")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "ok"
            assert data["model_loaded"] == True
    
    def test_health_check_model_error(self):
        """Test health check when model failed to load"""
        with patch('main.model_load_error', True):
            response = client.get("/health")
            assert response.status_code == 503
            data = response.json()
            assert "Model not loaded" in data["detail"]

class TestPredictEndpoint:
    """Test cases for the sentiment prediction endpoint"""
    
    def test_predict_success(self):
        """Test successful sentiment prediction"""
        mock_pipeline = MagicMock()
        mock_pipeline.return_value = [[
            {'label': 'LABEL_2', 'score': 0.8},
            {'label': 'LABEL_1', 'score': 0.15},
            {'label': 'LABEL_0', 'score': 0.05}
        ]]
        
        mock_model = MagicMock()
        mock_model.config.id2label = {0: "negative", 1: "neutral", 2: "positive"}
        mock_model.config.label2id = {"LABEL_0": 0, "LABEL_1": 1, "LABEL_2": 2}
        
        with patch('main.sentiment_pipeline', mock_pipeline), \
             patch('main.model', mock_model), \
             patch('main.model_load_error', False):
            
            response = client.post("/predict", json={"text": "This is a great product!"})
            assert response.status_code == 200
            data = response.json()
            assert data["sentiment"] == "positive"
            assert len(data["predictions"]) == 3
            assert data["predictions"][0]["label"] == "positive"
            assert data["predictions"][0]["score"] == 0.8
    
    def test_predict_empty_text(self):
        """Test prediction with empty text"""
        with patch('main.model_load_error', False):
            response = client.post("/predict", json={"text": ""})
            assert response.status_code == 400
            data = response.json()
            assert "Input text cannot be empty" in data["detail"]
    
    def test_predict_whitespace_text(self):
        """Test prediction with whitespace-only text"""
        with patch('main.model_load_error', False):
            response = client.post("/predict", json={"text": "   "})
            assert response.status_code == 400
            data = response.json()
            assert "Input text cannot be empty" in data["detail"]
    
    def test_predict_model_not_loaded(self):
        """Test prediction when model is not loaded"""
        with patch('main.model_load_error', True):
            response = client.post("/predict", json={"text": "Test text"})
            assert response.status_code == 503
            data = response.json()
            assert "Model not loaded" in data["detail"]
    
    def test_predict_pipeline_error(self):
        """Test prediction when pipeline returns unexpected format"""
        mock_pipeline = MagicMock()
        mock_pipeline.return_value = None  # Unexpected format
        
        with patch('main.sentiment_pipeline', mock_pipeline), \
             patch('main.model_load_error', False):
            
            response = client.post("/predict", json={"text": "Test text"})
            assert response.status_code == 500
            data = response.json()
            assert "Error processing text" in data["detail"]

class TestRequestValidation:
    """Test cases for request validation"""
    
    def test_invalid_json(self):
        """Test with invalid JSON payload"""
        response = client.post("/predict", json={})
        assert response.status_code == 422  # Validation error
    
    def test_missing_text_field(self):
        """Test with missing text field"""
        response = client.post("/predict", json={"wrong_field": "value"})
        assert response.status_code == 422  # Validation error

if __name__ == "__main__":
    pytest.main([__file__])

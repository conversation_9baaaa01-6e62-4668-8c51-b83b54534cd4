"""
Data Download Script for Multilingual Sentiment Analysis Platform

This script downloads the Amazon Reviews Multi dataset from Hugging Face
and creates sample files for development and testing.

Developed from Hasif's Workspace
"""

from datasets import load_dataset
import pandas as pd
import os

# Specify the dataset name
dataset_name = "amazon_reviews_multi"

# Specify the language subset to download (e.g., 'en', 'es', 'fr')
# Commented out to download all languages initially.
# languages = ['en']

# Create data directory if it doesn't exist
os.makedirs("data", exist_ok=True)

# Load the dataset
# If you specified languages, you might pass them to load_dataset if the function supports it directly,
# or you might need to filter after loading. For amazon_reviews_multi, the config name is the language.
# For example, to load only English: dataset = load_dataset(dataset_name, name='en')
# Loading all languages by default

print(f"Loading dataset: {dataset_name}")
# dataset = load_dataset(dataset_name, name="all_languages")  # This was causing issues, changed to load a specific language first for testing
dataset = load_dataset(dataset_name, name='en', split='train')  # Loading English dataset

# Print some basic information about the dataset
print("\nDataset features:")
print(dataset.features)
print(f"\nNumber of rows in 'train' split: {len(dataset)}")

# Save a small sample to a CSV file
sample_size = 1000
df = dataset.to_pandas()  # Convert to pandas DataFrame
sample_df = df.head(sample_size)

sample_csv_path = "data/amazon_reviews_multi_sample.csv"
sample_df.to_csv(sample_csv_path, index=False)

print(f"\nSaved a sample of {sample_size} rows to {sample_csv_path}")
print("\nScript finished.")
print("\nDeveloped from Hasif's Workspace")

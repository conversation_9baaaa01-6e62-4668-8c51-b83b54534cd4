"""
Utility functions for Multilingual Sentiment Analysis Platform Backend

This module contains helper functions and utilities used throughout the backend.

Developed from Hasif's Workspace
"""

import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import torch
from transformers import pipeline
from .config import settings, model_config


logger = logging.getLogger(__name__)


def setup_logging() -> None:
    """Set up logging configuration"""
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format=settings.log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("logs/backend.log") if settings.debug else logging.NullHandler()
        ]
    )


def get_device_info() -> Dict[str, Any]:
    """Get information about available compute devices"""
    device_info = {
        "cpu_available": True,
        "gpu_available": False,
        "gpu_count": 0,
        "gpu_names": [],
        "recommended_device": "cpu"
    }
    
    try:
        import torch
        if torch.cuda.is_available():
            device_info["gpu_available"] = True
            device_info["gpu_count"] = torch.cuda.device_count()
            device_info["gpu_names"] = [
                torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())
            ]
            device_info["recommended_device"] = "cuda:0"
        
        # Add memory information
        if device_info["gpu_available"]:
            gpu_memory = torch.cuda.get_device_properties(0).total_memory
            device_info["gpu_memory_gb"] = round(gpu_memory / (1024**3), 2)
        
    except ImportError:
        logger.warning("PyTorch not available, using CPU only")
    
    return device_info


def validate_text_input(text: str, max_length: int = 5000) -> Tuple[bool, Optional[str]]:
    """
    Validate text input for sentiment analysis
    
    Args:
        text: Input text to validate
        max_length: Maximum allowed text length
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not text:
        return False, "Text cannot be empty"
    
    if not text.strip():
        return False, "Text cannot be whitespace only"
    
    if len(text) > max_length:
        return False, f"Text length ({len(text)}) exceeds maximum allowed length ({max_length})"
    
    return True, None


def format_predictions(raw_predictions: List[Dict[str, Any]], model_config) -> List[Dict[str, Any]]:
    """
    Format raw model predictions into standardized format
    
    Args:
        raw_predictions: Raw predictions from the model
        model_config: Model configuration with label mappings
        
    Returns:
        Formatted predictions list
    """
    formatted_predictions = []
    
    for item in raw_predictions:
        internal_label = item['label']  # e.g., "LABEL_2"
        score = item['score']
        
        # Convert internal label to human-readable label
        try:
            if internal_label.startswith('LABEL_'):
                class_index = int(internal_label.split('_')[-1])
            else:
                # Direct mapping if available
                class_index = model_config.LABEL2ID.get(internal_label)
                if class_index is None:
                    logger.warning(f"Unknown label format: {internal_label}")
                    continue
            
            human_label = model_config.ID2LABEL.get(class_index, "unknown")
            
            formatted_predictions.append({
                "label": human_label,
                "score": round(score, 4)
            })
            
        except (ValueError, KeyError) as e:
            logger.warning(f"Error formatting prediction {item}: {e}")
            continue
    
    # Sort by score descending
    formatted_predictions.sort(key=lambda x: x['score'], reverse=True)
    
    return formatted_predictions


def get_top_prediction(predictions: List[Dict[str, Any]]) -> Tuple[str, float]:
    """
    Get the top prediction from a list of predictions
    
    Args:
        predictions: List of formatted predictions
        
    Returns:
        Tuple of (top_label, top_score)
    """
    if not predictions:
        return "unknown", 0.0
    
    top_prediction = max(predictions, key=lambda x: x['score'])
    return top_prediction['label'], top_prediction['score']


def measure_processing_time(func):
    """Decorator to measure function processing time"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Add processing time to result if it's a dictionary
        if isinstance(result, dict):
            result['processing_time'] = round(processing_time, 3)
        
        return result
    return wrapper


def sanitize_text(text: str) -> str:
    """
    Sanitize input text for processing
    
    Args:
        text: Input text to sanitize
        
    Returns:
        Sanitized text
    """
    # Remove excessive whitespace
    text = ' '.join(text.split())
    
    # Remove or replace potentially problematic characters
    # This is a basic implementation - extend as needed
    text = text.replace('\x00', '')  # Remove null bytes
    
    return text.strip()


def create_error_response(error_type: str, message: str, details: Optional[Dict] = None) -> Dict[str, Any]:
    """
    Create standardized error response
    
    Args:
        error_type: Type of error
        message: Error message
        details: Additional error details
        
    Returns:
        Error response dictionary
    """
    error_response = {
        "error": error_type,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }
    
    if details:
        error_response["details"] = details
    
    return error_response


def log_prediction_request(text: str, processing_time: float, sentiment: str) -> None:
    """
    Log prediction request for monitoring and debugging
    
    Args:
        text: Input text (truncated for logging)
        processing_time: Time taken to process
        sentiment: Predicted sentiment
    """
    text_preview = text[:100] + "..." if len(text) > 100 else text
    logger.info(
        f"Prediction completed - Text: '{text_preview}' | "
        f"Sentiment: {sentiment} | Time: {processing_time:.3f}s"
    )


def check_model_health(model, tokenizer) -> Dict[str, Any]:
    """
    Check the health of the loaded model
    
    Args:
        model: Loaded model instance
        tokenizer: Loaded tokenizer instance
        
    Returns:
        Model health information
    """
    health_info = {
        "model_loaded": model is not None,
        "tokenizer_loaded": tokenizer is not None,
        "model_type": None,
        "vocab_size": None,
        "max_length": None
    }
    
    if model is not None:
        health_info["model_type"] = model.__class__.__name__
        
        if hasattr(model, 'config'):
            health_info["vocab_size"] = getattr(model.config, 'vocab_size', None)
            health_info["max_length"] = getattr(model.config, 'max_position_embeddings', None)
    
    if tokenizer is not None:
        health_info["tokenizer_vocab_size"] = len(tokenizer.get_vocab()) if hasattr(tokenizer, 'get_vocab') else None
    
    return health_info


def get_system_info() -> Dict[str, Any]:
    """Get system information for debugging"""
    import platform
    import psutil
    
    return {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "cpu_count": psutil.cpu_count(),
        "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "memory_available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
        "disk_usage_percent": psutil.disk_usage('/').percent
    }

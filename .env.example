# Environment Variables for Multilingual Sentiment Analysis Platform
# Copy this file to .env and adjust values as needed
# Developed from <PERSON><PERSON>'s Workspace

# Application Settings
APP_NAME=Multilingual Sentiment Analysis Platform
APP_VERSION=1.0.0
DEBUG=false

# Server Settings
HOST=0.0.0.0
PORT=8000
RELOAD=false

# Model Settings
MODEL_DIR=./models/sentiment_model
MODEL_NAME=xlm-roberta-base
MAX_SEQUENCE_LENGTH=512

# Device Settings
USE_GPU=true
DEVICE_ID=0

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# API Settings
API_PREFIX=
CORS_ORIGINS=["*"]
MAX_REQUEST_SIZE=1048576

# Performance Settings
WORKERS=1
TIMEOUT=30

# Security Settings
ALLOWED_HOSTS=["*"]

# Frontend Settings
API_BASE_URL=http://localhost:8000
STREAMLIT_SERVER_ADDRESS=0.0.0.0
STREAMLIT_SERVER_PORT=8501

# Database Settings (if needed in future)
# DATABASE_URL=sqlite:///./sentiment_analysis.db

# Cache Settings (if needed in future)
# REDIS_URL=redis://localhost:6379

# Monitoring Settings (if needed in future)
# SENTRY_DSN=
# PROMETHEUS_ENABLED=false

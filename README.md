# Multilingual Sentiment Analysis Platform

A powerful platform for analyzing sentiment in text across multiple languages using state-of-the-art transformer models.

This project leverages Natural Language Processing (NLP) techniques to perform sentiment analysis on text in multiple languages, providing real-time sentiment classification with confidence scores. It aims to demonstrate practical NLP application development, from model training to deployment-ready web applications.

## ✨ Features

- **Multilingual Sentiment Analysis:** Supports sentiment classification (positive, negative, neutral) for multiple languages including English, Spanish, French, German, Japanese, and Chinese.
- **Real-time Analysis:** Instant sentiment prediction through a user-friendly web interface.
- **Confidence Scoring:** Provides detailed confidence scores for each sentiment category.
- **Interactive Visualizations:** Bar charts displaying sentiment confidence scores using Plotly.
- **RESTful API:** FastAPI backend providing robust endpoints for sentiment prediction.
- **Streamlit Frontend:** Interactive web application for easy text input and result visualization.
- **Containerized Deployment:** Dockerized components with Docker Compose for easy orchestration.
- **Comprehensive Testing:** Unit and integration tests for both backend and frontend components.

## 🛠️ Tech Stack

- **Backend:**
  - Python 3.9+
  - FastAPI: For building the REST API
  - Uvicorn: ASGI server for FastAPI
  - Hugging Face Transformers: For NLP models and tokenizers
  - PyTorch: Deep learning framework
  - XLM-RoBERTa: Multilingual transformer model

- **Frontend:**
  - Streamlit: For creating the interactive web application
  - Plotly: For data visualizations
  - Pandas: For data manipulation
  - Requests: For API communication

- **Machine Learning:**
  - Hugging Face Datasets: For data loading and processing
  - Scikit-learn: For metrics and evaluation
  - Accelerate: For distributed training

- **Testing:**
  - pytest: For unit and integration testing
  - pytest-cov: For test coverage analysis
  - requests-mock: For mocking API calls

- **Containerization:**
  - Docker & Docker Compose

## 📂 Project Structure

```
├── backend/                # FastAPI application and API logic
│   ├── __init__.py
│   ├── main.py             # FastAPI app definition and endpoints
│   ├── requirements.txt    # Backend Python dependencies
│   └── Dockerfile          # Dockerfile for backend
├── frontend/               # Streamlit application
│   ├── __init__.py
│   ├── app.py              # Streamlit application code
│   ├── requirements.txt    # Frontend Python dependencies
│   └── Dockerfile          # Dockerfile for frontend
├── data/                   # Data directory for datasets
│   ├── __init__.py
│   └── README.md           # Data documentation
├── models/                 # Trained models directory
│   ├── __init__.py
│   └── README.md           # Model documentation
├── scripts/                # Training and utility scripts
│   ├── __init__.py
│   ├── 01_download_data.py # Data download script
│   └── 02_train_sentiment_model.py # Model training script
├── tests/                  # Test suite
│   ├── __init__.py
│   ├── test_backend.py     # Backend API tests
│   └── test_frontend.py    # Frontend tests
├── docker-compose.yml      # Docker Compose configuration
├── requirements.txt        # Project dependencies
├── .gitignore             # Git ignore file
├── LICENSE                # MIT License
└── README.md              # This file
```

## ⚙️ Setup and Installation

### Prerequisites

- Python 3.9 or later
- `pip` (Python package installer)
- Docker and Docker Compose (recommended for easiest setup)

### Option 1: Using Docker (Recommended)

1. Clone this repository:
   ```bash
   git clone <repository_url>
   cd <repository_directory>
   ```

2. Build and start the services with Docker Compose:
   ```bash
   docker-compose up --build
   ```

3. Access the application:
   - Frontend: [http://localhost:8501](http://localhost:8501)
   - Backend API: [http://localhost:8000](http://localhost:8000)
   - API Documentation: [http://localhost:8000/docs](http://localhost:8000/docs)

### Option 2: Local Development

1. Clone this repository:
   ```bash
   git clone <repository_url>
   cd <repository_directory>
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   # On Windows:
   # venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up the backend:
   ```bash
   cd backend
   pip install -r requirements.txt
   cd ..
   ```

5. Set up the frontend:
   ```bash
   cd frontend
   pip install -r requirements.txt
   cd ..
   ```

6. Download data and train model (optional):
   ```bash
   python scripts/01_download_data.py
   python scripts/02_train_sentiment_model.py
   ```

7. Start the backend server:
   ```bash
   uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000
   ```

8. In a new terminal, start the frontend:
   ```bash
   streamlit run frontend/app.py
   ```

9. Access the application:
   - Frontend: [http://localhost:8501](http://localhost:8501)
   - Backend API: [http://localhost:8000](http://localhost:8000)
   - API Documentation: [http://localhost:8000/docs](http://localhost:8000/docs)

## 📖 User Guide

### Web Interface

1. Access the application at [http://localhost:8501](http://localhost:8501)
2. Enter your text in the text area (supports multiple languages)
3. Click "Analyze Sentiment"
4. View the results including:
   - Overall sentiment classification
   - Confidence scores for each sentiment category
   - Interactive visualization of results

### API Usage

The backend provides a REST API for sentiment analysis.

#### Endpoint: `/predict` (POST)

- **URL:** `http://localhost:8000/predict`
- **Request Body (JSON):**
  ```json
  {
    "text": "Your text to analyze here..."
  }
  ```
- **Response (JSON):**
  ```json
  {
    "sentiment": "positive",
    "predictions": [
      {"label": "positive", "score": 0.85},
      {"label": "neutral", "score": 0.10},
      {"label": "negative", "score": 0.05}
    ]
  }
  ```

#### Health Check: `/health` (GET)

- **URL:** `http://localhost:8000/health`
- **Response:** API health status and model loading status

## 🧪 Running Tests

1. Install test dependencies:
   ```bash
   pip install pytest pytest-cov requests-mock
   ```

2. Run all tests:
   ```bash
   pytest
   ```

3. Run tests with coverage:
   ```bash
   pytest --cov=.
   ```

4. Generate HTML coverage report:
   ```bash
   pytest --cov=. --cov-report=html
   ```

## 🚀 Model Training

The platform uses XLM-RoBERTa fine-tuned on the Amazon Reviews Multi dataset:

1. **Download Data:**
   ```bash
   python scripts/01_download_data.py
   ```

2. **Train Model:**
   ```bash
   python scripts/02_train_sentiment_model.py
   ```

The trained model will be saved to `models/sentiment_model/` and automatically loaded by the API.

## 🌍 Supported Languages

- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Japanese (ja)
- Chinese (zh)

## 📊 Model Performance

The model is trained to classify sentiment into three categories:
- **Positive:** 4-5 star ratings
- **Neutral:** 3 star ratings  
- **Negative:** 1-2 star ratings

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

*Developed from Hasif's Workspace*

version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    image: sentiment-analysis-backend:latest
    container_name: sentiment_analysis_backend_service
    ports:
      - "8000:8000"  # Expose API on host port 8000
    # The model is bundled into the image by default as per backend/Dockerfile.
    # If the model directory were very large or updated independently,
    # you could mount it as a volume. However, this requires the model
    # to be present on the host machine at the specified path.
    # For simplicity and portability of this docker-compose, we rely on the model
    # being copied into the image during the 'docker build' step.
    # volumes:
    #   - ./models/sentiment_model:/app/models/sentiment_model:ro
    restart: unless-stopped  # Changed from always to unless-stopped for more graceful manual stops
    networks:
      - sentiment_net

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    image: sentiment-analysis-frontend:latest
    container_name: sentiment_analysis_frontend_service
    ports:
      - "8501:8501"  # Expose Streamlit frontend on host port 8501
    environment:
      # This is key for frontend to find backend in Docker Compose network.
      # 'backend' is the service name of the backend service in this docker-compose file.
      - API_BASE_URL=http://backend:8000
      # For Streamlit when running behind a proxy or in Docker, good to set these:
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0  # Listen on all interfaces within the container
      - STREAMLIT_SERVER_PORT=8501
      # The following is usually not needed unless specific Streamlit features require it
      # or you are running behind a proxy that needs to know the base path.
      # - STREAMLIT_SERVER_BASE_URL_PATH=/
    depends_on:
      - backend  # Ensures backend service starts before frontend attempts to connect
    restart: unless-stopped  # Changed from always to unless-stopped
    networks:
      - sentiment_net

networks:
  sentiment_net:
    driver: bridge

# Note: This docker-compose setup assumes that frontend/app.py has been modified
# to use the API_BASE_URL environment variable, as discussed in deployment_guide.md.
# The default value in frontend/app.py ("http://localhost:8000") will be overridden
# by API_BASE_URL=http://backend:8000 when run via this docker-compose file.

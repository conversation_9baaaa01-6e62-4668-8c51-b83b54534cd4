import logging
import time
from datetime import datetime
from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from transformers import (
    AutoTokenizer,
    AutoModelForSequenceClassification,
    TextClassificationPipeline,
)
import torch

from .config import (
    settings,
    model_config,
    get_model_path,
    validate_model_path,
    get_device_config,
)
from .models import (
    SentimentRequest,
    SentimentResponse,
    SentimentPrediction,
    HealthResponse,
    ErrorResponse,
    BatchSentimentRequest,
    BatchSentimentResponse,
)
from .utils import (
    setup_logging,
    get_device_info,
    validate_text_input,
    format_predictions,
    get_top_prediction,
    measure_processing_time,
    sanitize_text,
    create_error_response,
    log_prediction_request,
    check_model_health,
)

# --- Setup Logging ---
setup_logging()
logger = logging.getLogger(__name__)

# --- Global Variables for Model and Pipeline ---
tokenizer = None
model = None
sentiment_pipeline = None
model_load_error = False
app_start_time = time.time()

# --- FastAPI App Initialization ---
app = FastAPI(
    title=settings.app_name,
    description="A powerful sentiment analysis API supporting multiple languages",
    version=settings.app_version,
    debug=settings.debug,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# --- Model Loading Logic & Startup Event ---
@app.on_event("startup")
async def startup_event():
    global tokenizer, model, sentiment_pipeline, model_load_error

    model_path = get_model_path()
    device, device_id = get_device_config()

    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    logger.info(f"Attempting to load model from: {model_path}")
    logger.info(f"Using device: {device}")
    logger.info(f"Device info: {get_device_info()}")

    if not validate_model_path():
        logger.error(f"Model directory not found or incomplete: {model_path}")
        model_load_error = True
        return

    try:
        logger.info("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(str(model_path))

        logger.info("Loading model...")
        model = AutoModelForSequenceClassification.from_pretrained(str(model_path))

        # Ensure the model has proper label mappings
        if model.config.id2label is None:
            model.config.id2label = model_config.ID2LABEL
        if model.config.label2id is None:
            model.config.label2id = model_config.LABEL2ID

        logger.info("Creating sentiment pipeline...")
        sentiment_pipeline = TextClassificationPipeline(
            model=model, tokenizer=tokenizer, device=device_id, return_all_scores=True
        )

        logger.info(
            "✅ Model and tokenizer loaded successfully. Sentiment pipeline created."
        )
        logger.info(f"Model info: {check_model_health(model, tokenizer)}")

    except OSError as e:
        logger.error(f"❌ OSError during model loading: {e}")
        logger.error("Check if model files are downloaded and accessible")
        model_load_error = True
    except Exception as e:
        logger.error(f"❌ Unexpected error during model loading: {e}")
        model_load_error = True


# --- Endpoints ---
@app.get("/health")
async def health_check():
    if model_load_error:
        raise HTTPException(
            status_code=503, detail="Model not loaded. API is unhealthy."
        )
    return {"status": "ok", "model_loaded": not model_load_error}


@app.post("/predict", response_model=SentimentResponse)
async def predict_sentiment(request: SentimentRequest):
    global sentiment_pipeline, model_load_error

    if model_load_error or sentiment_pipeline is None:
        logger.error("Prediction attempt while model is not loaded.")
        raise HTTPException(
            status_code=503, detail="Model not loaded. Cannot perform predictions."
        )

    if not request.text or request.text.isspace():
        raise HTTPException(status_code=400, detail="Input text cannot be empty.")

    logger.info(f'Received prediction request for text: "{request.text[:100]}..."')

    try:
        raw_predictions = sentiment_pipeline(request.text)

        if (
            not raw_predictions
            or not isinstance(raw_predictions, list)
            or not isinstance(raw_predictions[0], list)
        ):
            logger.error(f"Unexpected output format from pipeline: {raw_predictions}")
            raise HTTPException(
                status_code=500, detail="Error processing text with sentiment pipeline."
            )

        # raw_predictions is a list containing a list of dictionaries: [[{'label': 'LABEL_X', 'score': Y}, ...]]
        scores_list = raw_predictions[0]

        # Convert internal labels (LABEL_0, LABEL_1, ...) to human-readable labels
        # using model.config.id2label which we ensured is populated.
        formatted_predictions = []
        for item in scores_list:
            internal_label = item["label"]  # e.g., "LABEL_2"
            class_index = model.config.label2id.get(
                internal_label
            )  # Get the numeric ID

            if class_index is None:
                # Fallback if direct mapping from LABEL_X fails
                try:
                    class_index = int(internal_label.split("_")[-1])
                except ValueError:
                    logger.warning(
                        f"Could not parse class index from label {internal_label}"
                    )
                    continue

            human_label = model.config.id2label.get(class_index, "unknown")
            formatted_predictions.append(
                SentimentResponseItem(label=human_label, score=item["score"])
            )

        if not formatted_predictions:
            logger.error(f"Could not format predictions: {scores_list}")
            raise HTTPException(
                status_code=500, detail="Error formatting prediction results."
            )

        # Determine the sentiment with the highest score
        top_sentiment_item = max(formatted_predictions, key=lambda x: x.score)
        top_sentiment_label = top_sentiment_item.label

        logger.info(f"Prediction successful. Top sentiment: {top_sentiment_label}")

        return SentimentResponse(
            predictions=formatted_predictions, sentiment=top_sentiment_label
        )

    except Exception as e:
        logger.exception(
            f'An unexpected error occurred during prediction for text: "{request.text[:100]}..."'
        )
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )


# --- Main Block (for local running) ---
if __name__ == "__main__":
    import uvicorn

    logger.info("Starting Uvicorn server for local development...")
    # Note: The model loading happens on app startup. If MODEL_DIR is incorrect,
    # the server will start, but /predict and /health (if model check is strict) will fail.
    uvicorn.run(
        "main:app", host="0.0.0.0", port=8000, reload=True
    )  # reload=True for dev
    # For production, use: uvicorn.run(app, host="0.0.0.0", port=8000)

    # The reload flag means uvicorn will use the string "main:app" to import the app.
    # If not using reload, can pass app object directly.
    # The `startup_event` will be triggered by Uvicorn when the application starts.

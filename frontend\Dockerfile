# Start from a Python base image
FROM python:3.9-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file from the frontend directory first to leverage Docker layer caching
COPY requirements.txt .

# Install Streamlit and other dependencies from requirements.txt
# Ensure streamlit, requests, pandas, plotly are in requirements.txt
# Adding streamlit here directly as it's core to this frontend
# and ensures it's present even if requirements.txt is minimal.
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir streamlit

# Copy the rest of the frontend directory into the /app directory in the container
COPY . /app

# Expose the default Streamlit port
EXPOSE 8501

# Command to run the Streamlit application
# Looks for an 'app.py' file in the /app directory.
CMD ["streamlit", "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0"]

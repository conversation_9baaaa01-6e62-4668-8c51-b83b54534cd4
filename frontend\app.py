import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import logging
import os

# --- Configuration & Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API_URL: For local development, this points to the FastAPI backend.
# In a deployed environment (e.g., Docker Compose, Kubernetes),
# this might be 'http://backend:8000/predict' where 'backend' is the service name.
# For Docker Compose, API_BASE_URL might be 'http://backend:8000'. For other deployments, use environment variables.
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_URL = f"{API_BASE_URL}/predict"  # Construct the full API URL

SENTIMENT_COLORS = {
    "positive": "green",
    "negative": "red", 
    "neutral": "blue",
    "unknown": "grey"  # Fallback color
}

# --- Streamlit App Structure ---
st.set_page_config(
    page_title="Multilingual Sentiment Analysis Platform", 
    layout="wide", 
    initial_sidebar_state="auto"
)

st.title("Multilingual Sentiment Analysis Platform 🌍")
st.markdown(
    """
    Enter text in various languages (English, Spanish, French, German, Japanese, Chinese - depending on the trained model) 
    and click 'Analyze Sentiment' to see the results. The backend API will process the text and return the sentiment scores.
    """
)

# --- Input Section ---
st.header("📝 Analyze Text Sentiment")
user_text = st.text_area("Enter text here:", height=150, placeholder="Type or paste your text...")
analyze_button = st.button("Analyze Sentiment", type="primary")

# --- Analysis and Display Section ---
if analyze_button:
    if user_text and user_text.strip():
        st.markdown("---")  # Visual separator
        st.subheader("📊 Analysis Results")
        
        with st.spinner("🧠 Analyzing your text..."):
            payload = {"text": user_text}
            
            try:
                response = requests.post(API_URL, json=payload, timeout=30)  # Added timeout
                
                if response.status_code == 200:
                    results = response.json()
                    top_sentiment = results.get("sentiment", "N/A").capitalize()
                    predictions = results.get("predictions", [])
                    
                    # Display Overall Sentiment prominently
                    st.metric(label="Overall Sentiment", value=top_sentiment)
                    
                    if predictions:
                        df = pd.DataFrame(predictions)
                        # Rename columns for better display in chart and table
                        df.rename(columns={'label': 'Sentiment', 'score': 'Confidence'}, inplace=True)
                        
                        # Ensure 'Sentiment' column is capitalized for consistency if not already
                        df['Sentiment'] = df['Sentiment'].apply(lambda x: x.capitalize())
                        
                        # Sort by confidence for better visualization
                        df = df.sort_values(by="Confidence", ascending=False)
                        
                        # Create Bar Chart
                        fig = px.bar(
                            df,
                            x="Sentiment",
                            y="Confidence",
                            color="Sentiment",
                            title="Sentiment Confidence Scores",
                            color_discrete_map={s.capitalize(): c for s, c in SENTIMENT_COLORS.items()},
                            labels={"Confidence": "Confidence Score", "Sentiment": "Predicted Sentiment"},
                            height=400
                        )
                        fig.update_layout(xaxis_title="Sentiment", yaxis_title="Confidence Score")
                        st.plotly_chart(fig, use_container_width=True)
                        
                        # Display detailed scores in an expander
                        with st.expander("View Detailed Scores"):
                            st.dataframe(df, use_container_width=True)
                    else:
                        st.info("No detailed predictions were returned by the API.")
                        
                elif response.status_code == 400:
                    # Bad request from API (e.g. empty text, though frontend checks too)
                    st.error(f"⚠️ Error from API: {response.status_code} - {response.json().get('detail', response.text)}")
                elif response.status_code == 503:
                    # Model not loaded or API unhealthy
                    st.error(f"⚠️ API Service Unavailable: {response.status_code} - {response.json().get('detail', 'The model may not be loaded yet. Please try again in a moment.')}")
                else:
                    # Other API errors
                    st.error(f"⚠️ Error from API: {response.status_code} - {response.text}")
                    
            except requests.exceptions.Timeout:
                logger.error(f"API request timed out: {API_URL}")
                st.error("⏳ The request to the sentiment analysis API timed out. The server might be busy. Please try again later.")
            except requests.exceptions.ConnectionError:
                logger.error(f"Failed to connect to API at {API_URL}")
                st.error(f"🔌 Failed to connect to the sentiment analysis API at {API_URL}. Please ensure the backend service is running and accessible.")
            except Exception as e:
                # Catch any other unexpected errors
                logger.exception("An unexpected error occurred in the frontend.")
                st.error(f"An unexpected error occurred: {e}")
    else:
        st.warning("✏️ Please enter some text to analyze.")

# --- Styling (Optional Placeholder) ---
# st.markdown(
#     """
#     <style>
#     # Custom CSS here
#     </style>
#     """,
#     unsafe_allow_html=True
# )

# TODO: Extract helper functions for easier unit testing (e.g., API response parsing, color mapping logic).

st.markdown("---")
st.markdown("Developed from Hasif's Workspace.")

# Add a small section about the API URL for transparency/debugging
with st.sidebar:
    st.header("⚙️ Configuration")
    st.markdown(f"**Backend API URL:** `{API_URL}`")
    st.markdown("---")
    st.markdown(
        """
        **About this App:**
        This application uses a Hugging Face transformer model fine-tuned for multilingual sentiment analysis. 
        The backend is built with FastAPI and the frontend with Streamlit.
        """
    )

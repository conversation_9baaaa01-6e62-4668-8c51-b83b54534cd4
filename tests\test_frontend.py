"""
Frontend Tests for Multilingual Sentiment Analysis Platform

Tests for the Streamlit frontend functionality and helper functions.

Developed from Hasif's Workspace
"""

import pytest
import requests_mock
import pandas as pd
from unittest.mock import patch, MagicMock
import sys
import os

# Add the frontend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'frontend'))

class TestAPIIntegration:
    """Test cases for API integration"""
    
    def test_successful_api_response(self):
        """Test handling of successful API response"""
        mock_response = {
            "sentiment": "positive",
            "predictions": [
                {"label": "positive", "score": 0.8},
                {"label": "neutral", "score": 0.15},
                {"label": "negative", "score": 0.05}
            ]
        }
        
        with requests_mock.Mocker() as m:
            m.post("http://localhost:8000/predict", json=mock_response)
            
            # Test the API call
            import requests
            response = requests.post("http://localhost:8000/predict", json={"text": "Great product!"})
            assert response.status_code == 200
            data = response.json()
            assert data["sentiment"] == "positive"
            assert len(data["predictions"]) == 3
    
    def test_api_timeout(self):
        """Test handling of API timeout"""
        with requests_mock.Mocker() as m:
            m.post("http://localhost:8000/predict", exc=requests.exceptions.Timeout)
            
            import requests
            with pytest.raises(requests.exceptions.Timeout):
                requests.post("http://localhost:8000/predict", json={"text": "Test"}, timeout=1)
    
    def test_api_connection_error(self):
        """Test handling of API connection error"""
        with requests_mock.Mocker() as m:
            m.post("http://localhost:8000/predict", exc=requests.exceptions.ConnectionError)
            
            import requests
            with pytest.raises(requests.exceptions.ConnectionError):
                requests.post("http://localhost:8000/predict", json={"text": "Test"})
    
    def test_api_error_response(self):
        """Test handling of API error responses"""
        with requests_mock.Mocker() as m:
            m.post("http://localhost:8000/predict", status_code=400, json={"detail": "Bad request"})
            
            import requests
            response = requests.post("http://localhost:8000/predict", json={"text": ""})
            assert response.status_code == 400
            data = response.json()
            assert "Bad request" in data["detail"]

class TestDataProcessing:
    """Test cases for data processing functions"""
    
    def test_predictions_to_dataframe(self):
        """Test conversion of predictions to DataFrame"""
        predictions = [
            {"label": "positive", "score": 0.8},
            {"label": "neutral", "score": 0.15},
            {"label": "negative", "score": 0.05}
        ]
        
        df = pd.DataFrame(predictions)
        df.rename(columns={'label': 'Sentiment', 'score': 'Confidence'}, inplace=True)
        df['Sentiment'] = df['Sentiment'].apply(lambda x: x.capitalize())
        df = df.sort_values(by="Confidence", ascending=False)
        
        assert len(df) == 3
        assert df.iloc[0]['Sentiment'] == 'Positive'
        assert df.iloc[0]['Confidence'] == 0.8
        assert list(df.columns) == ['Sentiment', 'Confidence']
    
    def test_sentiment_colors_mapping(self):
        """Test sentiment color mapping"""
        sentiment_colors = {
            "positive": "green",
            "negative": "red", 
            "neutral": "blue",
            "unknown": "grey"
        }
        
        assert sentiment_colors["positive"] == "green"
        assert sentiment_colors["negative"] == "red"
        assert sentiment_colors["neutral"] == "blue"
        assert sentiment_colors["unknown"] == "grey"

class TestEnvironmentConfiguration:
    """Test cases for environment configuration"""
    
    def test_api_base_url_default(self):
        """Test default API base URL"""
        with patch.dict(os.environ, {}, clear=True):
            import os
            api_base_url = os.getenv("API_BASE_URL", "http://localhost:8000")
            assert api_base_url == "http://localhost:8000"
    
    def test_api_base_url_custom(self):
        """Test custom API base URL from environment"""
        with patch.dict(os.environ, {"API_BASE_URL": "http://backend:8000"}):
            import os
            api_base_url = os.getenv("API_BASE_URL", "http://localhost:8000")
            assert api_base_url == "http://backend:8000"

class TestInputValidation:
    """Test cases for input validation"""
    
    def test_empty_text_validation(self):
        """Test validation of empty text input"""
        text = ""
        is_valid = text and text.strip()
        assert not is_valid
    
    def test_whitespace_text_validation(self):
        """Test validation of whitespace-only text input"""
        text = "   \n\t  "
        is_valid = text and text.strip()
        assert not is_valid
    
    def test_valid_text_validation(self):
        """Test validation of valid text input"""
        text = "This is a valid text input"
        is_valid = text and text.strip()
        assert is_valid

if __name__ == "__main__":
    pytest.main([__file__])
